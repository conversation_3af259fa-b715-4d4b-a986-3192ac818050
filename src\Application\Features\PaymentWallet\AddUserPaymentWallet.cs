using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.PaymentWallet;

public class AddUserPaymentWalletController : ApiControllerBase
{
    [HttpPost]
    [Authorize("admin")]
    public async Task<IActionResult> AddUserPaymentWallet([FromBody] AddUserPaymentWalletCommand request)
    {
        var result = await Mediator.Send(request, HttpContext.RequestAborted);

        return result.Match(id => Ok(id), Problem);
    }
}

public sealed class AddUserPaymentWalletCommand
    : IRequest<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public int UserId { get; init; }
    public string PaymentWalletId { get; init; } = null!;
}

public sealed class AddUserPaymentWalletCommandValidator : AbstractValidator<AddUserPaymentWalletCommand>
{
    public AddUserPaymentWalletCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty();
        RuleFor(x => x.PaymentWalletId)
            .NotEmpty();
    }
}

public sealed class AddUserPaymentWalletCommandHandler(
    ApplicationDbContext dbContext)
    : IRequestHandler<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(AddUserPaymentWalletCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .Include(x => x.WalletInformation)
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig?.WalletInformation != null)
        {
            userConfig.WalletInformation.UpdatePaymentWalletId(WalletId.Parse(request.PaymentWalletId));
        }
        else
        {
            var userWalletInformation = UserWalletInformation.Create(
                request.UserId,
                WalletId.Parse(request.PaymentWalletId));

            userConfig = UserConfig.Create(userWalletInformation);
            dbContext.UserConfigs.Add(userConfig);
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Success : Error.Failure();
    }
}