using DispatchR.Requests;
using DispatchR.Requests.Send;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Domain;
using Zify.Settlement.Application.Domain.ValueObjects;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace Zify.Settlement.Application.Features.PaymentWallet;

public sealed class AddUserPaymentWalletCommand
    : IRequest<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public int UserId { get; init; }
    public string PaymentWalletId { get; init; } = null!;
}

public sealed class AddUserPaymentWalletCommandValidator : AbstractValidator<AddUserPaymentWalletCommand>
{
    public AddUserPaymentWalletCommandValidator()
    {
        RuleFor(x => x.UserId)
            .NotEmpty();
        RuleFor(x => x.PaymentWalletId)
            .NotEmpty();
    }
}

public sealed class AddUserPaymentWalletCommandHandler(
    ApplicationDbContext dbContext)
    : IRequestHandler<AddUserPaymentWalletCommand, Task<ErrorOr<Success>>>
{
    public async Task<ErrorOr<Success>> Handle(AddUserPaymentWalletCommand request, CancellationToken cancellationToken)
    {
        var userConfig = await dbContext.UserConfigs
            .Include(x => x.WalletInformation)
            .Where(x => x.UserId == request.UserId)
            .FirstOrDefaultAsync(cancellationToken);

        if (userConfig?.WalletInformation != null)
        {
            userConfig.WalletInformation.UpdatePaymentWalletId(WalletId.Parse(request.PaymentWalletId));
        }
        else
        {
            var userWalletInformation = UserWalletInformation.Create(
                request.UserId,
                WalletId.Parse(request.PaymentWalletId));

            userConfig = UserConfig.Create(userWalletInformation);
            dbContext.UserConfigs.Add(userConfig);
        }

        var result = await dbContext.SaveChangesAsync(cancellationToken);
        return result > 0 ? Result.Success : Error.Failure();
    }
}

// ============================================================================
// ENDPOINT DEFINITION (Vertical Slice Architecture)
// ============================================================================

/// <summary>
/// Request model for adding payment wallet to user
/// </summary>
/// <param name="PaymentWalletId">The payment wallet identifier</param>
public record AddUserPaymentWalletRequest(
    [Required(ErrorMessage = "Payment wallet ID is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Payment wallet ID must be between 1 and 100 characters")]
    string PaymentWalletId);

/// <summary>
/// Success response model
/// </summary>
/// <param name="Message">Success message</param>
public record AddUserPaymentWalletResponse(string Message);

/// <summary>
/// Endpoint definition for AddUserPaymentWallet feature
/// </summary>
public static class AddUserPaymentWalletEndpoint
{
    /// <summary>
    /// Maps the AddUserPaymentWallet endpoint
    /// </summary>
    /// <param name="app">The endpoint route builder</param>
    public static void MapAddUserPaymentWalletEndpoint(this IEndpointRouteBuilder app)
    {
        app.MapPost("/api/v{version:apiVersion}/payment-wallet/users/{userId:int}/wallet", HandleAsync)
            .WithName("AddUserPaymentWallet")
            .WithSummary("Add payment wallet to user")
            .WithDescription("Associates a payment wallet with a specific user. Requires admin privileges.")
            .WithTags("Payment Wallet")
            .WithOpenApi()
            .Produces<AddUserPaymentWalletResponse>(StatusCodes.Status200OK)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces<ProblemDetails>(StatusCodes.Status500InternalServerError)
            .RequireAuthorization("admin");
    }

    /// <summary>
    /// Handles the AddUserPaymentWallet request
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="request">The payment wallet request</param>
    /// <param name="mediator">The mediator instance</param>
    /// <param name="validator">The validator instance</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response or problem details</returns>
    private static async Task<Results<Ok<AddUserPaymentWalletResponse>, ValidationProblem, ProblemHttpResult>> HandleAsync(
        [FromRoute] int userId,
        [FromBody] AddUserPaymentWalletRequest request,
        [FromServices] IMediator mediator,
        [FromServices] IValidator<AddUserPaymentWalletCommand> validator,
        CancellationToken cancellationToken = default)
    {
        var command = new AddUserPaymentWalletCommand
        {
            UserId = userId,
            PaymentWalletId = request.PaymentWalletId
        };

        // Validate the command
        var validationResult = await validator.ValidateAsync(command, cancellationToken);
        if (!validationResult.IsValid)
        {
            var validationErrors = validationResult.Errors.ToDictionary(
                error => error.PropertyName,
                error => new[] { error.ErrorMessage });

            return TypedResults.ValidationProblem(validationErrors);
        }

        var result = await mediator.Send(command, cancellationToken);

        return result.Match(
            success => TypedResults.Ok(new AddUserPaymentWalletResponse("Payment wallet added successfully")),
            HandleErrors);
    }

    /// <summary>
    /// Handles errors and converts them to appropriate HTTP responses
    /// </summary>
    /// <param name="errors">List of errors</param>
    /// <returns>Appropriate HTTP result</returns>
    private static Results<Ok<AddUserPaymentWalletResponse>, ValidationProblem, ProblemHttpResult> HandleErrors(List<Error> errors)
    {
        if (errors.Count == 0)
        {
            return TypedResults.Problem(
                title: "An error occurred",
                statusCode: StatusCodes.Status500InternalServerError);
        }

        if (errors.All(error => error.Type == ErrorType.Validation))
        {
            var validationErrors = errors.ToDictionary(
                error => error.Code,
                error => new[] { error.Description });

            return TypedResults.ValidationProblem(validationErrors);
        }

        var firstError = errors[0];
        var statusCode = firstError.Type switch
        {
            ErrorType.Conflict => StatusCodes.Status409Conflict,
            ErrorType.Validation => StatusCodes.Status400BadRequest,
            ErrorType.NotFound => StatusCodes.Status404NotFound,
            ErrorType.Unauthorized => StatusCodes.Status403Forbidden,
            _ => StatusCodes.Status500InternalServerError,
        };

        return TypedResults.Problem(
            title: firstError.Description,
            statusCode: statusCode);
    }
}