using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using System.Net;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;
using Zify.Settlement.Application.Infrastructure.Persistence;

namespace PayPing.Settlement.Api.IntegrationTests;

public class PaymentWalletEndpointsTests : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly CustomWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public PaymentWalletEndpointsTests(CustomWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task AddUserPaymentWallet_WithoutAuthentication_ReturnsUnauthorized()
    {
        // Arrange
        var request = new
        {
            paymentWalletId = Guid.NewGuid().ToString()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/payment-wallet/users/1/wallet", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.Unauthorized);
    }

    [Fact]
    public async Task AddUserPaymentWallet_WithValidRequest_ReturnsSuccess()
    {
        // Arrange
        var token = await GetAuthTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var request = new
        {
            paymentWalletId = Guid.NewGuid().ToString()
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/payment-wallet/users/1/wallet", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var successResponse = JsonSerializer.Deserialize<SuccessResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        successResponse.Should().NotBeNull();
        successResponse!.Message.Should().Be("Payment wallet added successfully");
    }

    [Fact]
    public async Task AddUserPaymentWallet_WithInvalidWalletId_ReturnsBadRequest()
    {
        // Arrange
        var token = await GetAuthTokenAsync();
        _client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var request = new
        {
            paymentWalletId = "invalid-guid" // Invalid GUID format
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/payment-wallet/users/1/wallet", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private async Task<string> GetAuthTokenAsync()
    {
        var tokenRequest = new
        {
            username = "testadmin",
            userId = 1,
            roles = new[] { "admin" }
        };

        var response = await _client.PostAsJsonAsync("/api/v1/auth/token", tokenRequest);
        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        return tokenResponse!.AccessToken;
    }

    private record TokenResponse(
        string AccessToken,
        string TokenType,
        int ExpiresIn,
        DateTime ExpiresAt);

    private record SuccessResponse(string Message);
}
