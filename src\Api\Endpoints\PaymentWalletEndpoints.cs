using DispatchR.Requests;
using ErrorOr;
using FluentValidation;
using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using System.ComponentModel.DataAnnotations;
using Zify.Settlement.Application.Features.PaymentWallet;

namespace Zify.Settlement.Api.Endpoints;

/// <summary>
/// Payment Wallet endpoints
/// </summary>
public static class PaymentWalletEndpoints
{
    /// <summary>
    /// Maps payment wallet endpoints
    /// </summary>
    /// <param name="app">The endpoint route builder</param>
    public static void Map(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/v{version:apiVersion}/payment-wallet")
            .WithTags("Payment Wallet")
            .WithOpenApi();

        group.MapPost("/users/{userId:int}/wallet", AddUserPaymentWalletAsync)
            .WithName("AddUserPaymentWallet")
            .WithSummary("Add payment wallet to user")
            .WithDescription("Associates a payment wallet with a specific user. Requires admin privileges.")
            .Produces<SuccessResponse>(StatusCodes.Status200OK)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .Produces(StatusCodes.Status401Unauthorized)
            .Produces(StatusCodes.Status403Forbidden)
            .Produces<ProblemDetails>(StatusCodes.Status500InternalServerError)
            .RequireAuthorization("admin");
    }

    /// <summary>
    /// Adds a payment wallet to a user
    /// </summary>
    /// <param name="userId">The user ID</param>
    /// <param name="request">The payment wallet request</param>
    /// <param name="mediator">The mediator instance</param>
    /// <param name="validator">The validator instance</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Success response or problem details</returns>
    private static async Task<Results<Ok<SuccessResponse>, ValidationProblem, ProblemHttpResult>> AddUserPaymentWalletAsync(
        [FromRoute] int userId,
        [FromBody] AddUserPaymentWalletRequest request,
        [FromServices] IMediator mediator,
        [FromServices] IValidator<AddUserPaymentWalletCommand> validator,
        CancellationToken cancellationToken = default)
    {
        var command = new AddUserPaymentWalletCommand
        {
            UserId = userId,
            PaymentWalletId = request.PaymentWalletId
        };

        // Validate the command
        var validationResult = await validator.ValidateAsync(command, cancellationToken);
        if (!validationResult.IsValid)
        {
            var validationErrors = validationResult.Errors.ToDictionary(
                error => error.PropertyName,
                error => new[] { error.ErrorMessage });

            return TypedResults.ValidationProblem(validationErrors);
        }

        var result = await mediator.Send(command, cancellationToken);

        return result.Match(
            success => TypedResults.Ok(new SuccessResponse("Payment wallet added successfully")),
            HandleErrors);
    }

    /// <summary>
    /// Handles errors and converts them to appropriate HTTP responses
    /// </summary>
    /// <param name="errors">List of errors</param>
    /// <returns>Appropriate HTTP result</returns>
    private static Results<Ok<SuccessResponse>, ValidationProblem, ProblemHttpResult> HandleErrors(List<Error> errors)
    {
        if (errors.Count == 0)
        {
            return TypedResults.Problem(
                title: "An error occurred",
                statusCode: StatusCodes.Status500InternalServerError);
        }

        if (errors.All(error => error.Type == ErrorType.Validation))
        {
            var validationErrors = errors.ToDictionary(
                error => error.Code,
                error => new[] { error.Description });

            return TypedResults.ValidationProblem(validationErrors);
        }

        var firstError = errors[0];
        var statusCode = firstError.Type switch
        {
            ErrorType.Conflict => StatusCodes.Status409Conflict,
            ErrorType.Validation => StatusCodes.Status400BadRequest,
            ErrorType.NotFound => StatusCodes.Status404NotFound,
            ErrorType.Unauthorized => StatusCodes.Status403Forbidden,
            _ => StatusCodes.Status500InternalServerError,
        };

        return TypedResults.Problem(
            title: firstError.Description,
            statusCode: statusCode);
    }
}

/// <summary>
/// Request model for adding payment wallet to user
/// </summary>
/// <param name="PaymentWalletId">The payment wallet identifier</param>
public record AddUserPaymentWalletRequest(
    [Required(ErrorMessage = "Payment wallet ID is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Payment wallet ID must be between 1 and 100 characters")]
    string PaymentWalletId);

/// <summary>
/// Success response model
/// </summary>
/// <param name="Message">Success message</param>
public record SuccessResponse(string Message);
