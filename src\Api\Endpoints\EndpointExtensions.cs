namespace Zify.Settlement.Api.Endpoints;

/// <summary>
/// Extension methods for registering endpoints
/// </summary>
public static class EndpointExtensions
{
    /// <summary>
    /// Maps all payment wallet endpoints
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The web application for chaining</returns>
    public static WebApplication MapPaymentWalletEndpoints(this WebApplication app)
    {
        PaymentWalletEndpoints.Map(app);
        return app;
    }

    /// <summary>
    /// Maps all authentication endpoints
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The web application for chaining</returns>
    public static WebApplication MapAuthEndpoints(this WebApplication app)
    {
        AuthEndpoints.Map(app);
        return app;
    }

    /// <summary>
    /// Maps all API endpoints
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The web application for chaining</returns>
    public static WebApplication MapApiEndpoints(this WebApplication app)
    {
        app.MapAuthEndpoints();
        app.MapPaymentWalletEndpoints();
        return app;
    }
}
