namespace Zify.Settlement.Api.Endpoints;

/// <summary>
/// Extension methods for registering endpoints
/// Note: In Vertical Slice Architecture, most endpoints are registered directly
/// from their feature files. This file only contains shared/cross-cutting endpoints.
/// </summary>
public static class EndpointExtensions
{
    /// <summary>
    /// Maps authentication endpoints (shared across features)
    /// </summary>
    /// <param name="app">The web application</param>
    /// <returns>The web application for chaining</returns>
    public static WebApplication MapAuthEndpoints(this WebApplication app)
    {
        AuthEndpoints.Map(app);
        return app;
    }
}
