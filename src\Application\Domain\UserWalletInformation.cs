﻿using Ardalis.GuardClauses;
using Zify.Settlement.Application.Common;
using Zify.Settlement.Application.Domain.ValueObjects;

namespace Zify.Settlement.Application.Domain;

public class UserWalletInformation : AuditableEntity
{
    public int Id { get; private init; }
    public int UserId { get; private set; }
    public WalletId? SettlementWalletId { get; private set; }
    public WalletId PaymentWalletId { get; private set; }

    private UserWalletInformation()
    {
    }

    public static UserWalletInformation Create(int userId, WalletId paymentWalletId)
    {
        Guard.Against.NegativeOrZero(userId, nameof(userId));
        return new UserWalletInformation
        {
            UserId = userId,
            PaymentWalletId = paymentWalletId
        };
    }

    public void UpdatePaymentWalletId(WalletId paymentWalletId)
    {
        PaymentWalletId = paymentWalletId;
    }

    public void SetSettlementWalletId(Guid settlementWalletId)
    {
        Guard.Against.NullOrEmpty(settlementWalletId, nameof(settlementWalletId));
        SettlementWalletId = WalletId.Of(settlementWalletId);
    }
}