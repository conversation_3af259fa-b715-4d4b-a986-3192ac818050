using Microsoft.AspNetCore.Http.HttpResults;
using Microsoft.AspNetCore.Mvc;
using Microsoft.IdentityModel.Tokens;
using System.ComponentModel.DataAnnotations;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Zify.Settlement.Api.Endpoints;

/// <summary>
/// Authentication endpoints
/// </summary>
public static class AuthEndpoints
{
    /// <summary>
    /// Maps authentication endpoints
    /// </summary>
    /// <param name="app">The endpoint route builder</param>
    public static void Map(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/v{version:apiVersion}/auth")
            .WithTags("Authentication")
            .WithOpenApi();

        group.MapPost("/token", GenerateTokenAsync)
            .WithName("GenerateToken")
            .WithSummary("Generate JWT token for testing")
            .WithDescription("Generates a JWT token for testing purposes. In production, this should be replaced with proper authentication.")
            .Produces<TokenResponse>(StatusCodes.Status200OK)
            .Produces<ValidationProblemDetails>(StatusCodes.Status400BadRequest)
            .AllowAnonymous();
    }

    /// <summary>
    /// Generates a JWT token for testing purposes
    /// </summary>
    /// <param name="request">The token request</param>
    /// <param name="configuration">The configuration</param>
    /// <returns>JWT token response</returns>
    private static Results<Ok<TokenResponse>, ValidationProblem> GenerateTokenAsync(
        [FromBody] TokenRequest request,
        [FromServices] IConfiguration configuration)
    {
        var key = configuration["Jwt:Key"];
        var issuer = configuration["Jwt:Issuer"];
        var audience = configuration["Jwt:Audience"];
        var expiryInMinutes = configuration.GetValue<int>("Jwt:ExpiryInMinutes", 60);

        if (string.IsNullOrEmpty(key) || string.IsNullOrEmpty(issuer) || string.IsNullOrEmpty(audience))
        {
            var errors = new Dictionary<string, string[]>
            {
                ["Configuration"] = ["JWT configuration is missing or invalid"]
            };
            return TypedResults.ValidationProblem(errors);
        }

        var tokenHandler = new JwtSecurityTokenHandler();
        var keyBytes = Encoding.UTF8.GetBytes(key);
        var signingKey = new SymmetricSecurityKey(keyBytes);

        var claims = new List<Claim>
        {
            new(ClaimTypes.NameIdentifier, request.UserId.ToString()),
            new(ClaimTypes.Name, request.Username),
            new("userId", request.UserId.ToString())
        };

        // Add role claims
        foreach (var role in request.Roles)
        {
            claims.Add(new Claim(ClaimTypes.Role, role));
        }

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Expires = DateTime.UtcNow.AddMinutes(expiryInMinutes),
            Issuer = issuer,
            Audience = audience,
            SigningCredentials = new SigningCredentials(signingKey, SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        return TypedResults.Ok(new TokenResponse(
            tokenString,
            "Bearer",
            expiryInMinutes * 60,
            DateTime.UtcNow.AddMinutes(expiryInMinutes)));
    }
}

/// <summary>
/// Request model for token generation
/// </summary>
/// <param name="Username">The username</param>
/// <param name="UserId">The user ID</param>
/// <param name="Roles">The user roles</param>
public record TokenRequest(
    [Required(ErrorMessage = "Username is required")]
    [StringLength(100, MinimumLength = 1, ErrorMessage = "Username must be between 1 and 100 characters")]
    string Username,
    
    [Required(ErrorMessage = "User ID is required")]
    [Range(1, int.MaxValue, ErrorMessage = "User ID must be a positive number")]
    int UserId,
    
    [Required(ErrorMessage = "At least one role is required")]
    [MinLength(1, ErrorMessage = "At least one role is required")]
    string[] Roles);

/// <summary>
/// Response model for token generation
/// </summary>
/// <param name="AccessToken">The JWT access token</param>
/// <param name="TokenType">The token type (Bearer)</param>
/// <param name="ExpiresIn">Token expiry time in seconds</param>
/// <param name="ExpiresAt">Token expiry timestamp</param>
public record TokenResponse(
    string AccessToken,
    string TokenType,
    int ExpiresIn,
    DateTime ExpiresAt);
