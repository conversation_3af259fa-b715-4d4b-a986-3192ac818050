using FluentAssertions;
using Microsoft.AspNetCore.Mvc.Testing;
using System.Net;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace PayPing.Settlement.Api.IntegrationTests;

public class AuthEndpointsTests : IClassFixture<CustomWebApplicationFactory<Program>>
{
    private readonly CustomWebApplicationFactory<Program> _factory;
    private readonly HttpClient _client;

    public AuthEndpointsTests(CustomWebApplicationFactory<Program> factory)
    {
        _factory = factory;
        _client = _factory.CreateClient();
    }

    [Fact]
    public async Task GenerateToken_WithValidRequest_ReturnsToken()
    {
        // Arrange
        var request = new
        {
            username = "testuser",
            userId = 1,
            roles = new[] { "admin" }
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/auth/token", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.OK);
        
        var content = await response.Content.ReadAsStringAsync();
        var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(content, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });

        tokenResponse.Should().NotBeNull();
        tokenResponse!.AccessToken.Should().NotBeNullOrEmpty();
        tokenResponse.TokenType.Should().Be("Bearer");
        tokenResponse.ExpiresIn.Should().BeGreaterThan(0);
    }

    [Fact]
    public async Task GenerateToken_WithInvalidRequest_ReturnsBadRequest()
    {
        // Arrange
        var request = new
        {
            username = "", // Invalid: empty username
            userId = 0,    // Invalid: zero user ID
            roles = Array.Empty<string>() // Invalid: no roles
        };

        // Act
        var response = await _client.PostAsJsonAsync("/api/v1/auth/token", request);

        // Assert
        response.StatusCode.Should().Be(HttpStatusCode.BadRequest);
    }

    private record TokenResponse(
        string AccessToken,
        string TokenType,
        int ExpiresIn,
        DateTime ExpiresAt);
}
