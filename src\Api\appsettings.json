{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Jwt": {"Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!", "Issuer": "PayPing.Settlement.Api", "Audience": "PayPing.Settlement.Client", "ExpiryInMinutes": 60}, "ConnectionStrings": {"DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=PayPingSettlement;Trusted_Connection=true;MultipleActiveResultSets=true;"}}