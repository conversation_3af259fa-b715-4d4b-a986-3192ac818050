<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<AssemblyName>Zify.Settlement.$(MSBuildProjectName)</AssemblyName>
		<RootNamespace>$(AssemblyName)</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.5">
		  <PrivateAssets>all</PrivateAssets>
		  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Swashbuckle.AspNetCore" Version="8.1.1" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.5" />

		<PackageReference Include="Asp.Versioning.Http" Version="8.0.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.0.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Application\Application.csproj" />
	</ItemGroup>

</Project>
