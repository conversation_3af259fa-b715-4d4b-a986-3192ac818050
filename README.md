# PayPing Settlement API

A modern .NET 9 Web API built with Minimal APIs, following Clean Architecture and Vertical Slice Architecture patterns.

## 🚀 Features

- **Minimal APIs**: Modern .NET approach for building APIs
- **JWT Authentication**: Secure token-based authentication
- **Authorization Policies**: Role-based access control
- **Rate Limiting**: API rate limiting for protection
- **API Versioning**: Support for multiple API versions
- **Swagger/OpenAPI**: Comprehensive API documentation
- **CQRS with MediatR**: Command Query Responsibility Segregation
- **FluentValidation**: Request validation
- **ErrorOr Pattern**: Functional error handling
- **Entity Framework Core**: Database access with SQL Server
- **Health Checks**: Application health monitoring

## 🏗️ Architecture

The project follows **Vertical Slice Architecture** with these layers:

- **Api**: Minimal API endpoints and configuration
- **Application**: Business logic, commands, queries, and handlers
- **Domain**: Domain entities, value objects, and business rules
- **Infrastructure**: Data access, external services, and persistence

## 🛠️ Getting Started

### Prerequisites

- .NET 9 SDK
- SQL Server (LocalDB for development)
- Visual Studio 2022 or VS Code

### Installation

1. Clone the repository
2. Navigate to the project directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```

4. Update the database:
   ```bash
   dotnet ef database update --project src/Application --startup-project src/Api
   ```

5. Run the application:
   ```bash
   dotnet run --project src/Api
   ```

The API will be available at `https://localhost:7098` with Swagger UI at the root.

## 🔐 Authentication

### Getting a JWT Token

For testing purposes, use the `/api/v1/auth/token` endpoint:

```json
POST /api/v1/auth/token
{
  "username": "admin",
  "userId": 1,
  "roles": ["admin"]
}
```

Response:
```json
{
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "tokenType": "Bearer",
  "expiresIn": 3600,
  "expiresAt": "2024-01-01T12:00:00Z"
}
```

### Using the Token

Include the token in the Authorization header:
```
Authorization: Bearer <your-token>
```

## 📚 API Endpoints

### Payment Wallet

#### Add Payment Wallet to User
```http
POST /api/v1/payment-wallet/users/{userId}/wallet
Authorization: Bearer <token>
Content-Type: application/json

{
  "paymentWalletId": "123e4567-e89b-12d3-a456-************"
}
```

**Requirements:**
- Admin role required
- Valid GUID for paymentWalletId
- Positive integer for userId

## 🔧 Configuration

### JWT Settings

Configure JWT in `appsettings.json`:

```json
{
  "Jwt": {
    "Key": "YourSuperSecretKeyThatIsAtLeast32CharactersLong!",
    "Issuer": "PayPing.Settlement.Api",
    "Audience": "PayPing.Settlement.Client",
    "ExpiryInMinutes": 60
  }
}
```

### Database Connection

```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=(localdb)\\mssqllocaldb;Database=PayPingSettlement;Trusted_Connection=true;MultipleActiveResultSets=true;"
  }
}
```

## 🚦 Rate Limiting

The API includes rate limiting:
- 100 requests per minute per client
- Configurable in `Program.cs`

## 📊 Health Checks

Health check endpoint: `/health`

Returns application health status and dependencies.

## 🧪 Testing

### Using Swagger UI

1. Navigate to the root URL when running the application
2. Use the `/api/v1/auth/token` endpoint to get a JWT token
3. Click "Authorize" and enter `Bearer <your-token>`
4. Test the protected endpoints

### Using curl

```bash
# Get token
curl -X POST "https://localhost:7098/api/v1/auth/token" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "userId": 1,
    "roles": ["admin"]
  }'

# Use token to call protected endpoint
curl -X POST "https://localhost:7098/api/v1/payment-wallet/users/1/wallet" \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{
    "paymentWalletId": "123e4567-e89b-12d3-a456-************"
  }'
```

## 🏛️ Best Practices Implemented

- **Minimal APIs**: Modern, performant endpoint definitions
- **Typed Results**: Strongly-typed HTTP responses
- **Request/Response DTOs**: Clear API contracts
- **Validation**: Comprehensive input validation
- **Error Handling**: Consistent error responses
- **Security**: JWT authentication and authorization
- **Documentation**: OpenAPI/Swagger documentation
- **Monitoring**: Health checks and logging
- **Performance**: Rate limiting and caching headers

## 🔄 Migration from Controllers

This project was migrated from traditional controllers to Minimal APIs:

- Removed `ApiControllerBase` dependency
- Converted controller actions to static endpoint methods
- Maintained same business logic and validation
- Improved performance and reduced boilerplate
- Enhanced OpenAPI documentation

## 📝 Development Notes

- Uses DispatchR instead of MediatR for CQRS
- Implements ErrorOr pattern for functional error handling
- Follows vertical slice architecture for feature organization
- Uses value objects for domain modeling
- Implements audit trails for entities

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Add appropriate tests for new features
3. Update documentation for API changes
4. Ensure all health checks pass

## 📄 License

This project is licensed under the MIT License.